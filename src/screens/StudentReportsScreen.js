/**
 * Student Reports Screen
 * Displays comprehensive reports for students including attendance, grades, BPS, and homework
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faCalendarCheck,
  faChartBar,
  faStar,
  faTasks,
  faRefresh,
} from '@fortawesome/free-solid-svg-icons';

import { useTheme, getLanguageFontSizes } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  getAvailableReports,
  getStudentAttendanceReport,
  getStudentGradesReport,
  getStudentBPSReport,
  getStudentHomeworkReport,
} from '../services/reportsService';
import {
  <PERSON>hn<PERSON><PERSON><PERSON>,
  BarChart,
  StatsRow,
  EmptyState,
  LoadingState,
} from '../components';
import { isIPad, isTablet } from '../utils/deviceDetection';
import { createSmallShadow, createMediumShadow } from '../utils/commonStyles';

export default function StudentReportsScreen({ navigation, route }) {
  const { theme } = useTheme();
  const { t, currentLanguage } = useLanguage();
  const fontSizes = getLanguageFontSizes(currentLanguage);

  // Device detection
  const isIPadDevice = isIPad();
  const isTabletDevice = isTablet();

  // Route params
  const { userData } = route.params || {};

  // State management
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [availableReports, setAvailableReports] = useState([]);
  const [selectedReport, setSelectedReport] = useState('attendance');
  const [reportData, setReportData] = useState(null);
  const [dateRange] = useState({
    startDate: null,
    endDate: null,
  });

  // Load available reports on mount
  useEffect(() => {
    loadAvailableReports();
  }, []);

  // Load specific report when selection changes
  useEffect(() => {
    if (selectedReport && userData?.authCode) {
      loadReportData(selectedReport);
    }
  }, [selectedReport, dateRange]);

  // Safety check for theme
  if (!theme?.colors) {
    console.error('Theme not properly initialized in StudentReportsScreen');
    return null;
  }

  const loadAvailableReports = async () => {
    try {
      setLoading(true);
      const response = await getAvailableReports(userData?.authCode);

      if (response?.success) {
        setAvailableReports(response.reports || []);
        // Set first available report as default
        if (response.reports?.length > 0) {
          setSelectedReport(response.reports[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading available reports:', error);
      Alert.alert(t('error'), t('failedToLoadReports'));
    } finally {
      setLoading(false);
    }
  };

  const loadReportData = async (reportType) => {
    try {
      setLoading(true);
      let response = null;

      switch (reportType) {
        case 'attendance':
          response = await getStudentAttendanceReport(
            userData?.authCode,
            dateRange.startDate,
            dateRange.endDate
          );
          break;
        case 'grades':
          response = await getStudentGradesReport(
            userData?.authCode,
            dateRange.startDate,
            dateRange.endDate
          );
          break;
        case 'bps':
          response = await getStudentBPSReport(
            userData?.authCode,
            dateRange.startDate,
            dateRange.endDate
          );
          break;
        case 'homework':
          response = await getStudentHomeworkReport(
            userData?.authCode,
            dateRange.startDate,
            dateRange.endDate
          );
          break;
        default:
          throw new Error('Unknown report type');
      }

      if (response?.success) {
        setReportData(response);
      } else {
        throw new Error(response?.error || 'Failed to load report data');
      }
    } catch (error) {
      console.error(`Error loading ${reportType} report:`, error);
      Alert.alert(t('error'), t('failedToLoadReportData'));
      setReportData(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData(selectedReport);
    setRefreshing(false);
  };

  const getReportIcon = (reportId) => {
    switch (reportId) {
      case 'attendance':
        return faCalendarCheck;
      case 'grades':
        return faChartBar;
      case 'bps':
        return faStar;
      case 'homework':
        return faTasks;
      default:
        return faChartBar;
    }
  };

  const renderReportTabs = () => {
    if (!theme?.colors) return null;

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabsContainer}
        contentContainerStyle={styles.tabsContent}
      >
        {availableReports.map((report) => (
          <TouchableOpacity
            key={report.id}
            style={[
              styles.tab,
              selectedReport === report.id && styles.activeTab,
            ]}
            onPress={() => setSelectedReport(report.id)}
          >
            <FontAwesomeIcon
              icon={getReportIcon(report.id)}
              size={16}
              color={
                selectedReport === report.id
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <Text
              style={[
                styles.tabText,
                selectedReport === report.id && styles.activeTabText,
              ]}
            >
              {report.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderSummaryStats = () => {
    if (!theme?.colors) return null;
    if (!reportData?.data?.summary) return null;

    const summary = reportData.data.summary;
    let stats = [];

    switch (selectedReport) {
      case 'attendance':
        stats = [
          {
            label: t('totalDays'),
            value: summary.total_days,
            color: theme.colors.info,
          },
          {
            label: t('present'),
            value: summary.present_days,
            color: theme.colors.success,
          },
          {
            label: t('absent'),
            value: summary.absent_days,
            color: theme.colors.danger,
          },
          {
            label: t('attendanceRate'),
            value: `${summary.attendance_rate}%`,
            color: theme.colors.primary,
          },
        ];
        break;
      case 'grades':
        stats = [
          {
            label: t('overallAverage'),
            value: summary.overall_average?.toFixed(1),
            color: theme.colors.primary,
          },
          {
            label: t('highestGrade'),
            value: summary.highest_grade?.toFixed(1),
            color: theme.colors.success,
          },
          {
            label: t('lowestGrade'),
            value: summary.lowest_grade?.toFixed(1),
            color: theme.colors.warning,
          },
          {
            label: t('totalSubjects'),
            value: summary.total_subjects,
            color: theme.colors.info,
          },
        ];
        break;
      case 'bps':
        stats = [
          {
            label: t('totalPoints'),
            value: summary.total_points,
            color: theme.colors.primary,
          },
          {
            label: t('positivePoints'),
            value: summary.positive_points,
            color: theme.colors.success,
          },
          {
            label: t('negativePoints'),
            value: summary.negative_points,
            color: theme.colors.danger,
          },
          {
            label: t('totalRecords'),
            value: summary.total_records,
            color: theme.colors.info,
          },
        ];
        break;
      case 'homework':
        stats = [
          {
            label: t('totalHomework'),
            value: summary.total_homework,
            color: theme.colors.info,
          },
          {
            label: t('completed'),
            value: summary.completed_homework,
            color: theme.colors.success,
          },
          {
            label: t('pending'),
            value: summary.pending_homework,
            color: theme.colors.warning,
          },
          {
            label: t('completionRate'),
            value: `${summary.completion_rate}%`,
            color: theme.colors.primary,
          },
        ];
        break;
    }

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>{t('summary')}</Text>
        <StatsRow
          stats={stats}
          theme={theme}
          variant='detailed'
          showDividers={true}
        />
      </View>
    );
  };

  const renderChart = () => {
    if (!theme?.colors) return null;
    if (!reportData?.data?.chart_data) return null;

    const chartData = reportData.data.chart_data;
    const chartSize = isIPadDevice ? 280 : isTabletDevice ? 240 : 200;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.sectionTitle}>{t('visualization')}</Text>

        {chartData.type === 'doughnut' && (
          <DoughnutChart
            data={chartData.datasets[0].data}
            labels={chartData.labels}
            colors={chartData.datasets[0].backgroundColor}
            size={chartSize}
            theme={theme}
            showLabels={true}
            showValues={true}
          />
        )}

        {chartData.type === 'bar' && (
          <BarChart
            data={chartData.datasets[0].data}
            labels={chartData.labels}
            colors={chartData.datasets[0].backgroundColor}
            height={200}
            theme={theme}
            showValues={true}
            scrollable={chartData.labels.length > 6}
          />
        )}
      </View>
    );
  };

  if (loading && !reportData) {
    return <LoadingState message={t('loadingReports')} theme={theme} />;
  }

  const styles = createStyles(theme, fontSizes);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon
            icon={faArrowLeft}
            size={20}
            color={theme.colors.text}
          />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{t('myReports')}</Text>
          <Text style={styles.headerSubtitle}>{userData?.name}</Text>
        </View>

        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <FontAwesomeIcon
            icon={faRefresh}
            size={18}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Report Tabs */}
      {renderReportTabs()}

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {reportData ? (
          <>
            {renderSummaryStats()}
            {renderChart()}
          </>
        ) : (
          <EmptyState
            title={t('noReportData')}
            message={t('noReportDataMessage')}
            theme={theme}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme, fontSizes) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...Platform.select({
      ios: createSmallShadow(theme),
      android: { elevation: 2 },
    }),
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: fontSizes.large,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: fontSizes.small,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
    marginLeft: 8,
  },
  tabsContainer: {
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tabsContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  activeTab: {
    backgroundColor: theme.colors.primaryLight,
    borderColor: theme.colors.primary,
  },
  tabText: {
    fontSize: fontSizes.small,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    fontWeight: '500',
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  summaryContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    ...Platform.select({
      ios: createMediumShadow(theme),
      android: { elevation: 3 },
    }),
  },
  chartContainer: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    alignItems: 'center',
    ...Platform.select({
      ios: createMediumShadow(theme),
      android: { elevation: 3 },
    }),
  },
  sectionTitle: {
    fontSize: fontSizes.medium,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
});
