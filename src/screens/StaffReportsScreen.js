/**
 * Staff Reports Screen
 * Displays comprehensive reports for staff including class attendance, assessment, behavioral analytics, and homework analytics
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faCalendarCheck,
  faChartBar,
  faUsers,
  faTasks,
  faRefresh,
  faChevronDown,
} from '@fortawesome/free-solid-svg-icons';

import { useTheme, getLanguageFontSizes } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  getStaffClasses,
  getClassAttendanceReport,
  getClassAssessmentReport,
  getBehavioralAnalyticsReport,
  getHomeworkAnalyticsReport,
} from '../services/reportsService';
import {
  <PERSON>hnut<PERSON>hart,
  BarChart,
  StatsRow,
  EmptyState,
  LoadingState,
} from '../components';
import { isIPad, isTablet } from '../utils/deviceDetection';
import { createSmallShadow, createMediumShadow } from '../utils/commonStyles';

export default function StaffReportsScreen({ navigation, route }) {
  const { theme } = useTheme();
  const { t, currentLanguage } = useLanguage();
  const fontSizes = getLanguageFontSizes(currentLanguage);

  // Device detection
  const isIPadDevice = isIPad();
  const isTabletDevice = isTablet();

  // Route params
  const { userData } = route.params || {};

  // State management
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [availableClasses, setAvailableClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedReport, setSelectedReport] = useState('class-attendance');
  const [reportData, setReportData] = useState(null);
  const [showClassPicker, setShowClassPicker] = useState(false);

  // Report types for staff
  const reportTypes = [
    {
      id: 'class-attendance',
      name: t('classAttendance'),
      icon: faCalendarCheck,
      requiresClass: true,
    },
    {
      id: 'class-assessment',
      name: t('classAssessment'),
      icon: faChartBar,
      requiresClass: true,
    },
    {
      id: 'behavioral-analytics',
      name: t('behavioralAnalytics'),
      icon: faUsers,
      requiresClass: false,
    },
    {
      id: 'homework-analytics',
      name: t('homeworkAnalytics'),
      icon: faTasks,
      requiresClass: false,
    },
  ];

  // Load available classes on mount
  useEffect(() => {
    loadAvailableClasses();
  }, []);

  // Load specific report when selection changes
  useEffect(() => {
    if (selectedReport && userData?.authCode) {
      loadReportData(selectedReport);
    }
  }, [selectedReport, selectedClass]);

  // Safety check for theme
  if (!theme?.colors) {
    console.error('Theme not properly initialized in StaffReportsScreen');
    return null;
  }

  const loadAvailableClasses = async () => {
    try {
      setLoading(true);
      const response = await getStaffClasses(userData?.authCode);

      if (response?.success) {
        setAvailableClasses(response.classes || []);
        // Set first available class as default
        if (response.classes?.length > 0) {
          setSelectedClass(response.classes[0]);
        }
      }
    } catch (error) {
      console.error('Error loading available classes:', error);
      Alert.alert(t('error'), t('failedToLoadClasses'));
    } finally {
      setLoading(false);
    }
  };

  const loadReportData = async (reportType) => {
    try {
      setLoading(true);
      let response = null;

      switch (reportType) {
        case 'class-attendance':
          if (!selectedClass) return;
          response = await getClassAttendanceReport(
            userData?.authCode,
            selectedClass.classroom_id
          );
          break;
        case 'class-assessment':
          if (!selectedClass) return;
          response = await getClassAssessmentReport(
            userData?.authCode,
            selectedClass.classroom_id
          );
          break;
        case 'behavioral-analytics':
          response = await getBehavioralAnalyticsReport(
            userData?.authCode,
            selectedClass?.classroom_id
          );
          break;
        case 'homework-analytics':
          if (!selectedClass) return;
          response = await getHomeworkAnalyticsReport(
            userData?.authCode,
            selectedClass.classroom_id
          );
          break;
        default:
          throw new Error('Unknown report type');
      }

      if (response?.success) {
        setReportData(response);
      } else {
        throw new Error(response?.error || 'Failed to load report data');
      }
    } catch (error) {
      console.error(`Error loading ${reportType} report:`, error);
      Alert.alert(t('error'), t('failedToLoadReportData'));
      setReportData(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData(selectedReport);
    setRefreshing(false);
  };

  const renderClassPicker = () => {
    if (!theme?.colors) return null;

    const currentReport = reportTypes.find((r) => r.id === selectedReport);
    if (!currentReport?.requiresClass || availableClasses.length === 0)
      return null;

    return (
      <View style={styles.classPickerContainer}>
        <Text style={styles.classPickerLabel}>{t('selectClass')}</Text>
        <TouchableOpacity
          style={styles.classPicker}
          onPress={() => setShowClassPicker(!showClassPicker)}
        >
          <Text style={styles.classPickerText}>
            {selectedClass?.classroom_name || t('selectClass')}
          </Text>
          <FontAwesomeIcon
            icon={faChevronDown}
            size={14}
            color={theme.colors.textSecondary}
            style={{
              transform: [{ rotate: showClassPicker ? '180deg' : '0deg' }],
            }}
          />
        </TouchableOpacity>

        {showClassPicker && (
          <View style={styles.classDropdown}>
            {availableClasses.map((classItem) => (
              <TouchableOpacity
                key={classItem.classroom_id}
                style={[
                  styles.classOption,
                  selectedClass?.classroom_id === classItem.classroom_id &&
                    styles.selectedClassOption,
                ]}
                onPress={() => {
                  setSelectedClass(classItem);
                  setShowClassPicker(false);
                }}
              >
                <Text
                  style={[
                    styles.classOptionText,
                    selectedClass?.classroom_id === classItem.classroom_id &&
                      styles.selectedClassOptionText,
                  ]}
                >
                  {classItem.classroom_name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderReportTabs = () => {
    if (!theme?.colors) return null;

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabsContainer}
        contentContainerStyle={styles.tabsContent}
      >
        {reportTypes.map((report) => (
          <TouchableOpacity
            key={report.id}
            style={[
              styles.tab,
              selectedReport === report.id && styles.activeTab,
            ]}
            onPress={() => setSelectedReport(report.id)}
          >
            <FontAwesomeIcon
              icon={report.icon}
              size={16}
              color={
                selectedReport === report.id
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <Text
              style={[
                styles.tabText,
                selectedReport === report.id && styles.activeTabText,
              ]}
            >
              {report.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderSummaryStats = () => {
    if (!theme?.colors) return null;
    if (!reportData?.data?.summary) return null;

    const summary = reportData.data.summary;
    let stats = [];

    switch (selectedReport) {
      case 'class-attendance':
        stats = [
          {
            label: t('totalStudents'),
            value: summary.total_students,
            color: theme.colors.info,
          },
          {
            label: t('attendanceRate'),
            value: `${summary.attendance_rate}%`,
            color: theme.colors.primary,
          },
          {
            label: t('presentCount'),
            value: summary.present_count,
            color: theme.colors.success,
          },
          {
            label: t('absentCount'),
            value: summary.absent_count,
            color: theme.colors.danger,
          },
        ];
        break;
      case 'class-assessment':
        stats = [
          {
            label: t('classAverage'),
            value: summary.class_average?.toFixed(1),
            color: theme.colors.primary,
          },
          {
            label: t('highestScore'),
            value: summary.highest_score?.toFixed(1),
            color: theme.colors.success,
          },
          {
            label: t('lowestScore'),
            value: summary.lowest_score?.toFixed(1),
            color: theme.colors.warning,
          },
          {
            label: t('totalStudents'),
            value: summary.total_students,
            color: theme.colors.info,
          },
        ];
        break;
      case 'behavioral-analytics':
        stats = [
          {
            label: t('totalRecords'),
            value: summary.total_records,
            color: theme.colors.info,
          },
          {
            label: t('positiveRecords'),
            value: summary.positive_records,
            color: theme.colors.success,
          },
          {
            label: t('negativeRecords'),
            value: summary.negative_records,
            color: theme.colors.danger,
          },
          {
            label: t('positivePercentage'),
            value: `${summary.positive_percentage}%`,
            color: theme.colors.primary,
          },
        ];
        break;
      case 'homework-analytics':
        stats = [
          {
            label: t('totalAssigned'),
            value: summary.total_homework_assigned,
            color: theme.colors.info,
          },
          {
            label: t('completionRate'),
            value: `${summary.completion_rate}%`,
            color: theme.colors.primary,
          },
          {
            label: t('totalSubmissions'),
            value: summary.total_submissions,
            color: theme.colors.success,
          },
          {
            label: t('completedSubmissions'),
            value: summary.completed_submissions,
            color: theme.colors.success,
          },
        ];
        break;
    }

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>{t('summary')}</Text>
        <StatsRow
          stats={stats}
          theme={theme}
          variant='detailed'
          showDividers={true}
        />
      </View>
    );
  };

  const renderChart = () => {
    if (!theme?.colors) return null;
    if (!reportData?.data?.chart_data) return null;

    const chartData = reportData.data.chart_data;
    const chartSize = isIPadDevice ? 280 : isTabletDevice ? 240 : 200;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.sectionTitle}>{t('visualization')}</Text>

        {chartData.type === 'doughnut' && (
          <DoughnutChart
            data={chartData.datasets[0].data}
            labels={chartData.labels}
            colors={chartData.datasets[0].backgroundColor}
            size={chartSize}
            theme={theme}
            showLabels={true}
            showValues={true}
          />
        )}

        {chartData.type === 'bar' && (
          <BarChart
            data={chartData.datasets[0].data}
            labels={chartData.labels}
            colors={chartData.datasets[0].backgroundColor}
            height={200}
            theme={theme}
            showValues={true}
            scrollable={chartData.labels.length > 6}
          />
        )}
      </View>
    );
  };

  if (loading && !reportData) {
    return <LoadingState message={t('loadingReports')} theme={theme} />;
  }

  const styles = createStyles(theme, fontSizes);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon
            icon={faArrowLeft}
            size={20}
            color={theme.colors.text}
          />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{t('staffReports')}</Text>
          <Text style={styles.headerSubtitle}>{userData?.name}</Text>
        </View>

        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <FontAwesomeIcon
            icon={faRefresh}
            size={18}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Report Tabs */}
      {renderReportTabs()}

      {/* Class Picker */}
      {renderClassPicker()}

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {reportData ? (
          <>
            {renderSummaryStats()}
            {renderChart()}
          </>
        ) : (
          <EmptyState
            title={t('noReportData')}
            message={t('selectClassAndReport')}
            theme={theme}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme, fontSizes) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...Platform.select({
      ios: createSmallShadow(theme),
      android: { elevation: 2 },
    }),
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: fontSizes.large,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: fontSizes.small,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
    marginLeft: 8,
  },
  tabsContainer: {
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tabsContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  activeTab: {
    backgroundColor: theme.colors.primaryLight,
    borderColor: theme.colors.primary,
  },
  tabText: {
    fontSize: fontSizes.small,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    fontWeight: '500',
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  classPickerContainer: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  classPickerLabel: {
    fontSize: fontSizes.small,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    fontWeight: '500',
  },
  classPicker: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  classPickerText: {
    fontSize: fontSizes.medium,
    color: theme.colors.text,
    flex: 1,
  },
  classDropdown: {
    marginTop: 8,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    ...Platform.select({
      ios: createSmallShadow(theme),
      android: { elevation: 3 },
    }),
  },
  classOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  selectedClassOption: {
    backgroundColor: theme.colors.primaryLight,
  },
  classOptionText: {
    fontSize: fontSizes.medium,
    color: theme.colors.text,
  },
  selectedClassOptionText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  summaryContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    ...Platform.select({
      ios: createMediumShadow(theme),
      android: { elevation: 3 },
    }),
  },
  chartContainer: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    alignItems: 'center',
    ...Platform.select({
      ios: createMediumShadow(theme),
      android: { elevation: 3 },
    }),
  },
  sectionTitle: {
    fontSize: fontSizes.medium,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
});
